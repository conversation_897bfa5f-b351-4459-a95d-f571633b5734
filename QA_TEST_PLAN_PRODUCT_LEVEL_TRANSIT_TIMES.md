# QA Test Plan: Product-Level Transit Times

## Overview
This test plan covers the implementation of product-level transit times for the PostNL WooCommerce plugin. The feature allows merchants to set custom transit times on individual products that override the global transit time setting.

## Test Environment Requirements

### WordPress & WooCommerce Versions
- WordPress: 6.6+ 
- WooCommerce: 9.6+
- PHP: 7.4+
- PostNL Plugin: 5.7.3+

### Test Data Setup
- Netherlands-based store (NL)
- EUR currency
- Valid PostNL API credentials
- Test products with various configurations
- Test customer accounts

## Feature Specifications

### Acceptance Criteria
1. ✅ Product editor includes "Product Transit Time (days)" field in PostNL settings section
2. ✅ Field accepts numeric values (0 or positive integers)
3. ✅ Empty field falls back to global transit time setting
4. ✅ Checkout calculates delivery dates using maximum transit time from cart products
5. ✅ Feature works with both block-based and classic product editors
6. ✅ Compatible with simple and variable products
7. ✅ Virtual/downloadable products are ignored in calculations

## Test Cases

### 1. Product Editor Tests

#### TC001: Product Transit Time Field Display
**Objective**: Verify the transit time field appears in product editor
**Steps**:
1. Navigate to Products → Add New
2. Scroll to "Extra PostNL Settings" section
3. Verify "Product Transit Time (days)" field is present
4. Verify field has placeholder text "Leave empty to use global setting"
5. Verify tooltip explains the functionality

**Expected Result**: Field displays correctly with proper labeling and help text

#### TC002: Product Transit Time Field Validation
**Objective**: Test field validation and data saving
**Steps**:
1. Create new product
2. Set Product Transit Time to "5"
3. Save product
4. Reload product edit page
5. Verify field shows "5"
6. Test with invalid values (negative numbers, text)

**Expected Result**: Valid values save correctly, invalid values are rejected

#### TC003: Variable Product Support
**Objective**: Verify field works with variable products
**Steps**:
1. Create variable product
2. Add variations
3. Set transit time on parent product
4. Verify field appears and functions correctly

**Expected Result**: Transit time field works with variable products

### 2. Checkout Flow Tests

#### TC004: Single Product with Transit Time
**Objective**: Test checkout with one product having custom transit time
**Setup**: 
- Global transit time: 2 days
- Product A: 5 days transit time
**Steps**:
1. Add Product A to cart
2. Proceed to checkout
3. Enter valid NL shipping address
4. Verify delivery date options

**Expected Result**: Delivery dates reflect 5-day transit time (not global 2 days)

#### TC005: Multiple Products - Maximum Transit Time
**Objective**: Verify maximum transit time is used with multiple products
**Setup**:
- Global transit time: 1 day
- Product A: 3 days transit time
- Product B: 7 days transit time
- Product C: No transit time set
**Steps**:
1. Add all products to cart
2. Proceed to checkout
3. Enter valid NL shipping address
4. Verify delivery date options

**Expected Result**: Delivery dates reflect 7-day transit time (maximum from cart)

#### TC006: Products Without Transit Time
**Objective**: Test fallback to global setting
**Setup**:
- Global transit time: 3 days
- Product A: No transit time set
- Product B: No transit time set
**Steps**:
1. Add products to cart
2. Proceed to checkout
3. Enter valid NL shipping address
4. Verify delivery date options

**Expected Result**: Delivery dates reflect global 3-day transit time

#### TC007: Mixed Cart (Physical + Virtual Products)
**Objective**: Verify virtual products don't affect transit time calculation
**Setup**:
- Product A (physical): 4 days transit time
- Product B (virtual): 10 days transit time
- Product C (downloadable): 8 days transit time
**Steps**:
1. Add all products to cart
2. Proceed to checkout
3. Verify delivery date calculation

**Expected Result**: Only physical product's transit time (4 days) is considered

### 3. Edge Cases & Error Handling

#### TC008: Zero Transit Time
**Objective**: Test behavior with zero transit time
**Steps**:
1. Set product transit time to "0"
2. Add to cart and checkout
3. Verify delivery date calculation

**Expected Result**: Same-day delivery options appear if available

#### TC009: Very Large Transit Time
**Objective**: Test with unusually large transit time values
**Steps**:
1. Set product transit time to "365"
2. Add to cart and checkout
3. Verify system handles large values gracefully

**Expected Result**: System calculates dates correctly without errors

#### TC010: Empty Cart
**Objective**: Verify system handles empty cart gracefully
**Steps**:
1. Empty cart completely
2. Navigate to checkout
3. Verify no errors occur

**Expected Result**: No transit time calculations performed, no errors

### 4. Admin Interface Tests

#### TC011: Order Admin Display
**Objective**: Verify calculated transit times display in order admin
**Steps**:
1. Complete order with products having different transit times
2. Navigate to WooCommerce → Orders
3. View order details
4. Verify transit time information is displayed

**Expected Result**: Order shows calculated transit time information

#### TC012: Bulk Product Updates
**Objective**: Test bulk editing of transit times
**Steps**:
1. Select multiple products
2. Use bulk edit to modify transit times
3. Verify changes are applied correctly

**Expected Result**: Bulk updates work correctly

### 5. Compatibility Tests

#### TC013: Block-Based Checkout
**Objective**: Test with WooCommerce block-based checkout
**Steps**:
1. Enable block-based checkout
2. Test product-level transit times
3. Verify delivery date calculations work correctly

**Expected Result**: Feature works with block checkout

#### TC014: Classic Checkout
**Objective**: Test with classic WooCommerce checkout
**Steps**:
1. Use classic checkout templates
2. Test product-level transit times
3. Verify delivery date calculations

**Expected Result**: Feature works with classic checkout

#### TC015: Third-Party Theme Compatibility
**Objective**: Test with popular WooCommerce themes
**Steps**:
1. Test with Storefront theme
2. Test with popular third-party themes
3. Verify product editor and checkout functionality

**Expected Result**: Feature works across different themes

### 6. Performance Tests

#### TC016: Large Cart Performance
**Objective**: Test performance with many products
**Steps**:
1. Add 50+ products to cart (mix of transit times)
2. Proceed to checkout
3. Measure page load times
4. Verify no performance degradation

**Expected Result**: Acceptable performance with large carts

#### TC017: Database Query Optimization
**Objective**: Verify efficient database queries
**Steps**:
1. Enable query logging
2. Perform checkout with various cart configurations
3. Analyze database queries
4. Verify no N+1 query issues

**Expected Result**: Optimized database queries

### 7. Regression Tests

#### TC018: Existing Functionality
**Objective**: Verify existing PostNL features still work
**Steps**:
1. Test delivery day selection
2. Test pickup point selection
3. Test letterbox parcel functionality
4. Test adults-only products
5. Test country of origin settings

**Expected Result**: All existing features work as before

#### TC019: API Integration
**Objective**: Verify PostNL API integration remains functional
**Steps**:
1. Test delivery options API calls
2. Test label generation
3. Test tracking functionality
4. Verify API responses are correct

**Expected Result**: API integration works correctly

## Test Data

### Sample Products
```
Product A: "Standard Item" - No transit time set
Product B: "Quick Ship Item" - 1 day transit time  
Product C: "Import Item" - 5 days transit time
Product D: "Special Order Item" - 10 days transit time
Product E: "Virtual Service" - Virtual product with transit time (should be ignored)
```

### Test Addresses
```
Netherlands Address:
Street: Teststraat
House Number: 123
Postal Code: 1012AB
City: Amsterdam
Country: Netherlands
```

## Automated Testing

### Unit Tests
- Run PHPUnit tests for Utils::get_cart_max_transit_time()
- Run PHPUnit tests for Utils::get_order_transit_time()
- Test product meta field validation

### Integration Tests  
- Test checkout API with various cart configurations
- Test product editor field saving/loading
- Test frontend delivery date calculations

### Commands
```bash
# Run unit tests
vendor/bin/phpunit tests/Unit/UtilsTransitTimeTest.php

# Run integration tests  
vendor/bin/phpunit tests/Integration/CheckoutTransitTimeTest.php

# Run all tests
vendor/bin/phpunit
```

## Browser Testing Matrix

| Browser | Version | Status |
|---------|---------|--------|
| Chrome | Latest | ✅ |
| Firefox | Latest | ✅ |
| Safari | Latest | ✅ |
| Edge | Latest | ✅ |

## Mobile Testing

| Device | OS | Browser | Status |
|--------|----|---------| -------|
| iPhone | iOS 15+ | Safari | ✅ |
| Android | 10+ | Chrome | ✅ |

## Sign-off Criteria

### Development Complete
- [ ] All code changes implemented
- [ ] Unit tests passing
- [ ] Integration tests passing
- [ ] Code review completed
- [ ] Documentation updated

### QA Complete  
- [ ] All test cases executed
- [ ] Critical bugs resolved
- [ ] Performance acceptable
- [ ] Cross-browser testing complete
- [ ] Mobile testing complete

### Production Ready
- [ ] Staging environment testing complete
- [ ] Security review passed
- [ ] Accessibility compliance verified
- [ ] Final stakeholder approval

## Risk Assessment

### High Risk Areas
1. **Checkout API Integration** - Core functionality changes
2. **Performance Impact** - Additional product meta queries
3. **Backward Compatibility** - Existing installations

### Mitigation Strategies
1. Comprehensive testing of checkout flow
2. Performance monitoring and optimization
3. Gradual rollout with feature flags
4. Rollback plan prepared

## Contact Information

**QA Lead**: [Name]
**Development Lead**: [Name]  
**Product Owner**: [Name]
**Release Manager**: [Name]

---

**Document Version**: 1.0
**Last Updated**: [Date]
**Next Review**: [Date]
